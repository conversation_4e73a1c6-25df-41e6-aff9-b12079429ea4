import { useNodeConnections, useReactFlow } from "@xyflow/react";
import { useEffect, useState } from "react";
import type { SettingProps } from "@/components/flow/base/BasePanel/setting-panel";
import { useUpdateLiveConfig } from "@/hooks/node/kline-node/use-update-live-config";
import { getNodeDefaultInputHandleId, NodeType } from "@/types/node/index";
import type { KlineNodeData } from "@/types/node/kline-node";
import type { StartNode } from "@/types/node/start-node";
import { TradeMode } from "@/types/strategy";
import DataSourceSelector from "../components/data-source-selector";
import SymbolSelector from "../components/symbol-selector";

const KlineNodeLiveSettingPanel: React.FC<SettingProps> = ({ id, data }) => {
	const klineNodeData = data as KlineNodeData;

	const { getNode } = useReactFlow();

	// 已连接的start_node
	const [connectedStartNode, setConnectedStartNode] =
		useState<StartNode | null>(null);

	const connections = useNodeConnections({
		id,
		handleType: "target",
		handleId: getNodeDefaultInputHandleId(id, NodeType.KlineNode),
	});

	// 使用自定义hook管理实盘配置
	const { liveConfig, updateSelectedAccount, updateLiveSelectedSymbols } =
		useUpdateLiveConfig({
			id,
			initialLiveConfig: klineNodeData.liveConfig,
		});

	useEffect(() => {
		if (connections.length === 1) {
			// console.log('connections:', connections)
			// 获取已连接的start_node的id
			const startNodeId = connections[0].source;
			const startNodeNode = getNode(startNodeId);
			setConnectedStartNode(startNodeNode as StartNode);
		}
	}, [connections, getNode]);

	return (
		<div className="space-y-4">
			<DataSourceSelector
				startNode={connectedStartNode}
				tradeMode={TradeMode.LIVE}
				selectedAccount={liveConfig?.selectedAccount}
				onAccountChange={updateSelectedAccount}
			/>
			<SymbolSelector
				selectedSymbols={liveConfig?.selectedSymbols || []}
				onSymbolsChange={updateLiveSelectedSymbols}
				selectedDataSource={liveConfig?.selectedAccount}
			/>
		</div>
	);
};

export default KlineNodeLiveSettingPanel;
