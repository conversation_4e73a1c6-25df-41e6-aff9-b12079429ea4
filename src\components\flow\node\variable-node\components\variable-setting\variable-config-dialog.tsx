import { Clock, Filter } from "lucide-react";
import React, { useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	GetVariableType,
	StrategySysVariable,
	type TimerConfig,
	type VariableConfig,
} from "@/types/node/variable-node";
import SymbolSelector from "./symbol-selector";
import {
	generateVariableName,
	getVariableLabel,
	isDuplicateConfig,
} from "./utils";

interface VariableConfigDialogProps {
	id: string; // 节点id
	isOpen: boolean;
	isEditing: boolean;
	editingConfig?: VariableConfig;
	onOpenChange: (open: boolean) => void;
	onSave: (id: string, config: VariableConfig) => void;
	existingConfigs: VariableConfig[];
	editingIndex: number | null;
}

const VariableConfigDialog: React.FC<VariableConfigDialogProps> = ({
	id,
	isOpen,
	isEditing,
	editingConfig,
	onOpenChange,
	onSave,
	existingConfigs,
	editingIndex,
}) => {
	// 表单状态
	const [symbol, setSymbol] = React.useState<string>("");
	const [variableName, setVariableName] = React.useState<string>("");
	const [variable, setVariable] = React.useState<string>(
		StrategySysVariable.POSITION_NUMBER,
	);
	const [triggerType, setTriggerType] = React.useState<GetVariableType>(
		GetVariableType.CONDITION,
	);
	const [timerInterval, setTimerInterval] = React.useState<number>(1);
	const [timerUnit, setTimerUnit] = React.useState<
		"second" | "minute" | "hour" | "day"
	>("minute");
	const [isNameAutoGenerated, setIsNameAutoGenerated] =
		React.useState<boolean>(true);

	const resetForm = () => {
		setSymbol("");
		setVariableName("");
		setVariable(StrategySysVariable.POSITION_NUMBER);
		setTriggerType(GetVariableType.CONDITION);
		setTimerInterval(1);
		setTimerUnit("minute");
		setIsNameAutoGenerated(true);
	};

	// 当对话框打开时重置或恢复状态
	useEffect(() => {
		if (isOpen) {
			if (isEditing && editingConfig) {
				setSymbol(editingConfig.symbol || "");
				setVariableName(editingConfig.variableName);
				setVariable(editingConfig.variable);
				setTriggerType(editingConfig.getVariableType);
				if (editingConfig.timerConfig) {
					setTimerInterval(editingConfig.timerConfig.interval);
					setTimerUnit(editingConfig.timerConfig.unit);
				}
				setIsNameAutoGenerated(false); // 编辑时不是自动生成的
			} else {
				resetForm();
				// 新建时生成默认名称
				const defaultName = generateVariableName(
					StrategySysVariable.POSITION_NUMBER,
					existingConfigs.length,
				);
				setVariableName(defaultName);
				setIsNameAutoGenerated(true);
			}
		}
	}, [isOpen, isEditing, editingConfig, existingConfigs.length]);

	// 检查是否存在重复配置
	const isDuplicate = () => {
		return isDuplicateConfig(
			existingConfigs,
			editingIndex,
			symbol,
			variable,
			triggerType,
		);
	};

	// 当变量类型改变时，如果名称是自动生成的，则重新生成
	const handleVariableTypeChange = (newType: string) => {
		setVariable(newType);
		if (isNameAutoGenerated || !variableName.trim()) {
			const newName = generateVariableName(newType, existingConfigs.length);
			setVariableName(newName);
			setIsNameAutoGenerated(true);
		}
	};

	// 当用户手动修改变量名称时，标记为非自动生成
	const handleVariableNameChange = (value: string) => {
		setVariableName(value);
		setIsNameAutoGenerated(false);
	};

	const handleSave = () => {
		// 验证必填字段（变量名必填，交易对可选）
		if (!variableName.trim()) {
			return;
		}

		if (isDuplicate()) {
			return;
		}

		const timerConfig: TimerConfig | undefined =
			triggerType === GetVariableType.TIMER
				? {
						interval: timerInterval,
						unit: timerUnit,
					}
				: undefined;

		const variableConfig: VariableConfig = {
			configId: editingConfig?.configId || 0, // 编辑时保持原有id，新增时由主组件设置
			inputHandleId: editingConfig?.inputHandleId || "",
			symbol: symbol || null,
			getVariableType: triggerType,
			timerConfig,
			variableName: variableName.trim(),
			variable,
			variableValue: 0, // 默认值，实际运行时会更新
		};

		onSave(id, variableConfig);
		onOpenChange(false);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[424px]">
				<DialogHeader>
					<DialogTitle>
						{isEditing ? "编辑变量配置" : "添加变量配置"}
					</DialogTitle>
					<DialogDescription>
						配置变量的参数，包括交易对、触发方式和变量类型。
					</DialogDescription>
				</DialogHeader>
				<div className="flex flex-col gap-4 py-4">
					<div className="flex flex-col gap-2">
						<Label htmlFor="symbol" className="text-sm font-medium">
							交易对
						</Label>
						<div className="w-full">
							<SymbolSelector
								value={symbol}
								onChange={(value) => {
									setSymbol(value || "");
								}}
								allowEmpty={true}
							/>
						</div>
					</div>

					<div className="flex flex-col gap-2">
						<Label htmlFor="variableName" className="text-sm font-medium">
							变量名称
						</Label>
						<Input
							id="variableName"
							type="text"
							value={variableName}
							onChange={(e) => handleVariableNameChange(e.target.value)}
							placeholder="输入变量名称"
							className="w-full"
						/>
					</div>

					<div className="flex flex-col gap-2">
						<Label htmlFor="variable" className="text-sm font-medium">
							变量类型
						</Label>
						<Select value={variable} onValueChange={handleVariableTypeChange}>
							<SelectTrigger>
								<SelectValue placeholder="选择变量类型" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value={StrategySysVariable.POSITION_NUMBER}>
									{getVariableLabel(StrategySysVariable.POSITION_NUMBER)}
								</SelectItem>
								<SelectItem value={StrategySysVariable.Filled_ORDER_NUMBER}>
									{getVariableLabel(StrategySysVariable.Filled_ORDER_NUMBER)}
								</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div className="space-y-1">
						<Label className="text-sm font-medium">触发方式</Label>
						<div className="flex items-center space-x-6 pt-1">
							<div className="flex items-center space-x-2">
								<Checkbox
									id="condition-trigger"
									checked={triggerType === GetVariableType.CONDITION}
									onCheckedChange={(checked) => {
										if (checked) {
											setTriggerType(GetVariableType.CONDITION);
										}
									}}
								/>
								<Label
									htmlFor="condition-trigger"
									className="text-sm cursor-pointer flex items-center"
								>
									<Filter className="h-3.5 w-3.5 mr-1 text-orange-500" />
									条件触发
								</Label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="timer-trigger"
									checked={triggerType === GetVariableType.TIMER}
									onCheckedChange={(checked) => {
										if (checked) {
											setTriggerType(GetVariableType.TIMER);
										}
									}}
								/>
								<Label
									htmlFor="timer-trigger"
									className="text-sm cursor-pointer flex items-center"
								>
									<Clock className="h-3.5 w-3.5 mr-1 text-blue-500" />
									定时触发
								</Label>
							</div>
						</div>
					</div>

					{triggerType === GetVariableType.TIMER && (
						<div className="space-y-1">
							<Label className="text-sm font-medium">定时配置</Label>
							<div className="flex items-center space-x-2">
								<Input
									type="number"
									min="1"
									step="1"
									value={timerInterval}
									onChange={(e) =>
										setTimerInterval(parseInt(e.target.value) || 1)
									}
									className="h-8 w-20"
								/>
								<Select
									value={timerUnit}
									onValueChange={(
										value: "second" | "minute" | "hour" | "day",
									) => setTimerUnit(value)}
								>
									<SelectTrigger className="h-8 flex-1">
										<SelectValue placeholder="选择时间单位" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="second">秒</SelectItem>
										<SelectItem value="minute">分钟</SelectItem>
										<SelectItem value="hour">小时</SelectItem>
										<SelectItem value="day">天</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div className="flex flex-wrap gap-2 mt-2">
								<Badge
									variant="outline"
									className="bg-blue-50 text-blue-800 cursor-pointer hover:bg-blue-100"
									onClick={() => {
										setTimerInterval(1);
										setTimerUnit("second");
									}}
								>
									<Clock className="h-3 w-3 mr-1" />
									1s
								</Badge>

								<Badge
									variant="outline"
									className="bg-blue-50 text-blue-800 cursor-pointer hover:bg-blue-100"
									onClick={() => {
										setTimerInterval(1);
										setTimerUnit("minute");
									}}
								>
									<Clock className="h-3 w-3 mr-1" />
									1m
								</Badge>
								<Badge
									variant="outline"
									className="bg-blue-50 text-blue-800 cursor-pointer hover:bg-blue-100"
									onClick={() => {
										setTimerInterval(5);
										setTimerUnit("minute");
									}}
								>
									<Clock className="h-3 w-3 mr-1" />
									5m
								</Badge>
								<Badge
									variant="outline"
									className="bg-blue-50 text-blue-800 cursor-pointer hover:bg-blue-100"
									onClick={() => {
										setTimerInterval(1);
										setTimerUnit("hour");
									}}
								>
									<Clock className="h-3 w-3 mr-1" />
									1h
								</Badge>
								<Badge
									variant="outline"
									className="bg-blue-50 text-blue-800 cursor-pointer hover:bg-blue-100"
									onClick={() => {
										setTimerInterval(1);
										setTimerUnit("day");
									}}
								>
									<Clock className="h-3 w-3 mr-1" />
									1d
								</Badge>
							</div>
						</div>
					)}
				</div>
				<DialogFooter>
					<Button variant="outline" onClick={() => onOpenChange(false)}>
						取消
					</Button>
					<Button
						onClick={handleSave}
						disabled={!variableName.trim() || isDuplicate()}
					>
						保存
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default VariableConfigDialog;
