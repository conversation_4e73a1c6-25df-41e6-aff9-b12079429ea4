import { Search } from "lucide-react";
import type React from "react";
import { useMemo, useState } from "react";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { nodeList } from "@/constants/node-list";
import { useDndNodeStore } from "@/store/use-dnd-node-store";
import type { NodeItemProps } from "@/types/nodeCategory";

function NodeItem(props: NodeItemProps) {
	const { setDragNodeItem } = useDndNodeStore();

	const handleDragStart = (event: React.DragEvent<HTMLDivElement>) => {
		event.dataTransfer.setData("application/reactflow", props.nodeType);
		event.dataTransfer.effectAllowed = "move";

		// 使用 zustand store 设置拖拽数据
		setDragNodeItem({
			nodeId: props.nodeId,
			nodeType: props.nodeType,
			nodeName: props.nodeName,
			nodeDescription: props.nodeDescription || "",
			nodeColor: props.nodeColor,
			nodeData: props.nodeData,
		});
	};

	const handleDragEnd = () => {
		// 不要在这里立即清除 dragNodeItem，因为 onDrop 可能还没有触发
		// setDragNodeItem 应该在 onDrop 完成后清除
		// toast 也应该在实际添加节点后显示，而不是在拖拽结束时
	};

	return (
		<div
			className="flex items-center px-2 py-1.5 text-sm rounded hover:bg-gray-50 cursor-grab active:cursor-grabbing"
			draggable
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
		>
			{props.nodeName}
		</div>
	);
}

// 节点列表面板
const NodeListPanel: React.FC = () => {
	const [searchTerm, setSearchTerm] = useState("");

	const filteredCategories = useMemo(() => {
		const lowercasedFilter = searchTerm.trim().toLowerCase();
		if (!lowercasedFilter) {
			return nodeList;
		}

		return nodeList
			.map((item) => {
				const categoryTitleMatches = item.title
					.toLowerCase()
					.includes(lowercasedFilter);

				const filteredItems = item.items.filter((item) =>
					item.nodeName.toLowerCase().includes(lowercasedFilter),
				);

				if (categoryTitleMatches) {
					return item;
				}

				if (filteredItems.length > 0) {
					return { ...item, items: filteredItems };
				}

				return null;
			})
			.filter((item): item is NonNullable<typeof item> => item !== null);
	}, [searchTerm]);

	return (
		<div className="bg-white rounded-lg shadow-sm border border-gray-200 px-3 pb-3 pt-8 w-[240px] relative">
			{/* 搜索框 - 绝对定位到顶部，为关闭按钮留出空间 */}
			<div className="pt-0">
				<div className="relative">
					<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="搜索节点..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-9"
					/>
				</div>
			</div>
			<Separator className="mt-4" />

			{/* 节点列表 */}
			<ScrollArea className="h-[420px] ">
				<div className="space-y-3 py-2 pr-2">
					{filteredCategories.length > 0 ? (
						filteredCategories.map((item) => (
							<div key={item.title} className="space-y-1">
								<div className="text-xs text-gray-500 px-2 py-1 flex items-center">
									<item.icon className="w-3 h-3 mr-1" />
									{item.title}
								</div>
								<div className="space-y-1">
									{item.items.map((item) => (
										<NodeItem key={item.nodeId} {...item} />
									))}
								</div>
							</div>
						))
					) : (
						<div className="text-sm text-center text-gray-500 py-10">
							未找到匹配的节点
						</div>
					)}
				</div>
			</ScrollArea>
		</div>
	);
};

export default NodeListPanel;
