import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { Chart, CandlestickSeries, Pane, LineSeries, TimeScale, TimeScaleFitContentTrigger, SeriesApiRef } from "lightweight-charts-react-components";
import { generateLineData } from "./mock-data";
import { CrosshairMode, type IChartApi, type Time } from "lightweight-charts";
import { Card, CardContent} from "@/components/ui/card";
import { KlineLegend, useLegend } from "./legend";
import { useRealTimeKlineStore } from "./realTimeStore";
import { RealTimeControls } from "./RealTimeControls";
import type { BacktestChartConfig } from "@/types/chart/backtest-chart";
import { get_play_index } from "@/service/strategy-control/backtest-strategy-control";
import { createKlineStreamFromKey } from "@/hooks/obs/backtest-strategy-data-obs";
import { getInitialChartData } from "@/service/chart";
import type { Subscription } from "rxjs";
import type { K<PERSON> } from "@/types/kline";
import { parseK<PERSON> } from "@/utils/parse-key";
import type { KlineKey } from "@/types/symbol-key";

const lineData = generateLineData(100);
const lineData2 = generateLineData(100);


interface BacktestChartProps {
  strategyId: number;
  chartConfig: BacktestChartConfig;
}





const BacktestChart = ({ strategyId, chartConfig }: BacktestChartProps) => {
  const playIndexRef = useRef(0);
  const subscriptionsRef = useRef<Subscription[]>([]);

  const {
    data: realTimeData,
    setSeriesRef,
    updateSinglePoint,
    setData,
  } = useRealTimeKlineStore();

  // 转换 Kline 数据为 lightweight-charts 格式（时间戳转换为秒级）
  const convertKlineToChartData = useCallback((kline: Kline) => {
    return {
      time: Math.floor(kline.time / 1000) as Time, // 将毫秒级时间戳转换为秒级
      open: kline.open,
      high: kline.high,
      low: kline.low,
      close: kline.close,
    };
  }, []);

  // 转换 Kline 数组为 lightweight-charts 格式
  const convertKlineArrayToChartData = useCallback((klines: Kline[]) => {
    return klines.map(kline => convertKlineToChartData(kline));
  }, [convertKlineToChartData]);

  const { ref, legendData, onCrosshairMove } = useLegend({ data: convertKlineArrayToChartData(realTimeData) });



  // 加载初始数据
  const loadInitialData = useCallback(async () => {
    console.log("开始加载初始数据");

    // 从 chartConfig 获取 klineKeyStr
    const klineKeyStr = chartConfig.klineChartConfig.klineKeyStr;
    if (!klineKeyStr) {
      console.warn("未找到 klineKeyStr，无法加载初始数据");
      return;
    }

    // 解析 klineKey 获取交易对信息
    const klineKey = parseKey(klineKeyStr) as KlineKey;
    console.log("解析的 klineKey:", klineKey);

    try {
      // 如果 playIndex 大于 -1，则获取初始数据，-1 代表还未开始
      if (playIndexRef.current > -1) {
        console.log("获取初始 K线数据，playIndex:", playIndexRef.current);

        const initialKlines = (await getInitialChartData(
          klineKeyStr,
          playIndexRef.current,
          null,
        )) as Kline[];

        if (initialKlines && initialKlines.length > 0) {
          console.log(`获取到 ${initialKlines.length} 条初始 K线数据`);
          console.log("初始 K线数据:", initialKlines);

          // 设置原始数据到 store，转换在渲染时进行
          setData(initialKlines);
        } else {
          console.warn("未获取到初始 K线数据");
        }
      } else {
        console.log("playIndex 为 -1，跳过初始数据加载");
      }
    } catch (error) {
      console.error("加载初始数据失败:", error);
    }
  }, [chartConfig.klineChartConfig.klineKeyStr, setData]);

  // 修改 getPlayIndex 以在获取完成后立即加载数据
  const getPlayIndexAndLoadData = useCallback(async () => {
    try {
      const playIndex = await get_play_index(strategyId);
      playIndexRef.current = playIndex;
      console.log("playIndex", playIndex);

      // 获取到 playIndex 后立即加载初始数据
      await loadInitialData();
    } catch (error) {
      console.error("获取 playIndex 或加载初始数据失败:", error);
    }
  }, [strategyId, loadInitialData]);

  // 组件挂载时获取 playIndex 并加载初始数据
  useEffect(() => {
    getPlayIndexAndLoadData();
  }, [getPlayIndexAndLoadData]);

  // Chart 初始化回调 - 用于设置 observer 订阅
  const onChartInit = useCallback((_chart: IChartApi) => {
    console.log("Chart 初始化，开始设置 observer 订阅");

    // 清理之前的订阅
    subscriptionsRef.current.forEach(sub => sub.unsubscribe());
    subscriptionsRef.current = [];

    // 从 chartConfig 获取 klineKeyStr
    const klineKeyStr = chartConfig.klineChartConfig.klineKeyStr;
    if (!klineKeyStr) {
      console.warn("未找到 klineKeyStr，无法订阅数据流");
      return;
    }

    console.log("订阅 K线数据流，klineKeyStr:", klineKeyStr);

    // 订阅 K线数据流
    const klineStream = createKlineStreamFromKey(klineKeyStr, true);
    const klineSubscription = klineStream.subscribe((klineData: Kline[]) => {
      console.log("=== 收到 K线数据流更新 ===");
      console.log("K线数据长度:", klineData.length);

      if (klineData.length > 0) {
        const latestKline = klineData[klineData.length - 1];
        console.log("最新K线:", latestKline);

        // 1. 更新 store 中的原始数据
        const dataLimit = 10000;
        const newData = realTimeData.length >= dataLimit
          ? [...realTimeData.slice(1), latestKline]
          : [...realTimeData, latestKline];
        setData(newData);

        // 2. 转换数据格式并更新图表显示
        const chartData = convertKlineToChartData(latestKline);
        updateSinglePoint(chartData);
      }
    });

    subscriptionsRef.current.push(klineSubscription);
  }, [chartConfig.klineChartConfig.klineKeyStr, convertKlineToChartData, updateSinglePoint, realTimeData, setData]);

  // 设置series引用到store中，这样store就可以直接使用series.update方法
  useEffect(() => {
    const checkAndSetSeries = () => {
      if (ref.current) {
        const seriesApi = ref.current.api();
        if (seriesApi) {
          console.log("设置series引用到store:", seriesApi);
          setSeriesRef(ref.current);
          return true;
        } else {
          console.warn("series API尚未可用，稍后重试");
          return false;
        }
      }
      return false;
    };

    // 立即检查
    if (!checkAndSetSeries()) {
      // 如果立即检查失败，延迟重试
      const timer = setTimeout(() => {
        checkAndSetSeries();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [ref, setSeriesRef]);

  // 组件卸载时清理订阅
  useEffect(() => {
    return () => {
      // 清理 observer 订阅
      subscriptionsRef.current.forEach(sub => sub.unsubscribe());
      subscriptionsRef.current = [];
    };
  }, []);

  const chartOptions = {
    autoSize: true,
    width: 600,
    height: 400,
    grid: {
      vertLines: {
        visible: false,
      },
      horzLines: {
        visible: false,
      },
    },
    crosshair: {
      mode: CrosshairMode.Normal,
      vertLine: {
        style: 3,
        color: "#080F25",
      },
      horzLine: {
        style: 3,
        color: "#080F25",
      },
    },
    layout: {
      panes: {
        separatorColor: "#080F25",
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* 控制面板 */}
      <RealTimeControls />

      {/* 图表容器 */}
      <Card className="w-full">
        <CardContent>
          <div className="relative h-96 w-full">
            <Chart
              options={chartOptions}
              onCrosshairMove={onCrosshairMove}
              onInit={onChartInit}
            >
              <Pane>
                <CandlestickSeries
                  ref={ref}
                  data={convertKlineArrayToChartData(realTimeData)}
                  reactive={false}
                  // options={{
                  //   upColor: '#22c55e',
                  //   downColor: '#ef4444',
                  //   borderUpColor: '#22c55e',
                  //   borderDownColor: '#ef4444',
                  //   wickUpColor: '#22c55e',
                  //   wickDownColor: '#ef4444',
                  // }}
                />
                <KlineLegend klineSeriesData={legendData} />
              </Pane>
              {/* <TimeScale>
                <TimeScaleFitContentTrigger 
                  deps={resizeOnUpdate ? [realTimeData.length] : []} 
                />
              </TimeScale> */}
            </Chart>
            
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BacktestChart;