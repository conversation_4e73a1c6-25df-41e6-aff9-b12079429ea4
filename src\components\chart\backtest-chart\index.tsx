import React, { useCallback, useEffect, useRef, useState } from "react";
import { Chart, CandlestickSeries, Pane, LineSeries, TimeScale, TimeScaleFitContentTrigger, SeriesApiRef } from "lightweight-charts-react-components";
import { generateLineData } from "./mock-data";
import { CrosshairMode, type IChartApi } from "lightweight-charts";
import { Card, CardContent} from "@/components/ui/card";
import { KlineLegend, useLegend } from "./legend";
import { useRealTimeKlineStore } from "./realTimeStore";
import { RealTimeControls } from "./RealTimeControls";
import type { BacktestChartConfig } from "@/types/chart/backtest-chart";
import { get_play_index } from "@/service/strategy-control/backtest-strategy-control";
import { createKlineStreamFromKey } from "@/hooks/obs/backtest-strategy-data-obs";
import type { Subscription } from "rxjs";
import type { Kline } from "@/types/kline";

const lineData = generateLineData(100);
const lineData2 = generateLineData(100);


interface BacktestChartProps {
  strategyId: number;
  chartConfig: BacktestChartConfig;
}





const BacktestChart = ({ strategyId, chartConfig }: BacktestChartProps) => {
  const playIndexRef = useRef(0);
  const subscriptionsRef = useRef<Subscription[]>([]);

  const getPlayIndex = useCallback(() => {
    get_play_index(strategyId).then((playIndex) => {
      playIndexRef.current = playIndex;
      console.log("playIndex", playIndex);
    });
  }, [strategyId]);

  useEffect(() => {
    getPlayIndex();
  }, [getPlayIndex]);

  const {
    data: realTimeData,
    setSeriesRef,
    updateSinglePoint,
  } = useRealTimeKlineStore();

  const { ref, legendData, onCrosshairMove } = useLegend({ data: realTimeData });

  // 转换 Kline 数据为 lightweight-charts 格式
  const convertKlineToChartData = useCallback((kline: Kline) => {
    // 使用日期字符串格式，与现有数据格式保持一致
    const date = new Date(kline.timestamp);
    const timeStr = date.toISOString().split('T')[0]; // YYYY-MM-DD 格式

    return {
      time: timeStr,
      open: kline.open,
      high: kline.high,
      low: kline.low,
      close: kline.close,
    };
  }, []);

  // Chart 初始化回调 - 用于设置 observer 订阅
  const onChartInit = useCallback((_chart: IChartApi) => {
    console.log("Chart 初始化，开始设置 observer 订阅");

    // 清理之前的订阅
    subscriptionsRef.current.forEach(sub => sub.unsubscribe());
    subscriptionsRef.current = [];

    // 从 chartConfig 获取 klineKeyStr
    const klineKeyStr = chartConfig.klineChartConfig.klineKeyStr;
    if (!klineKeyStr) {
      console.warn("未找到 klineKeyStr，无法订阅数据流");
      return;
    }

    console.log("订阅 K线数据流，klineKeyStr:", klineKeyStr);

    // 订阅 K线数据流
    const klineStream = createKlineStreamFromKey(klineKeyStr, true);
    const klineSubscription = klineStream.subscribe((klineData: Kline[]) => {
      console.log("=== 收到 K线数据流更新 ===");
      console.log("K线数据长度:", klineData.length);

      if (klineData.length > 0) {
        const latestKline = klineData[klineData.length - 1];
        console.log("最新K线:", latestKline);

        // 转换数据格式并更新图表
        const chartData = convertKlineToChartData(latestKline);
        updateSinglePoint(chartData);
      }
    });

    subscriptionsRef.current.push(klineSubscription);
  }, [chartConfig.klineChartConfig.klineKeyStr, convertKlineToChartData, updateSinglePoint]);

  // 设置series引用到store中，这样store就可以直接使用series.update方法
  useEffect(() => {
    const checkAndSetSeries = () => {
      if (ref.current) {
        const seriesApi = ref.current.api();
        if (seriesApi) {
          console.log("设置series引用到store:", seriesApi);
          setSeriesRef(ref.current);
          return true;
        } else {
          console.warn("series API尚未可用，稍后重试");
          return false;
        }
      }
      return false;
    };

    // 立即检查
    if (!checkAndSetSeries()) {
      // 如果立即检查失败，延迟重试
      const timer = setTimeout(() => {
        checkAndSetSeries();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [ref, setSeriesRef]);

  // 组件卸载时清理订阅
  useEffect(() => {
    return () => {
      // 清理 observer 订阅
      subscriptionsRef.current.forEach(sub => sub.unsubscribe());
      subscriptionsRef.current = [];
    };
  }, []);

  const chartOptions = {
    autoSize: true,
    width: 600,
    height: 400,
    grid: {
      vertLines: {
        visible: false,
      },
      horzLines: {
        visible: false,
      },
    },
    crosshair: {
      mode: CrosshairMode.Normal,
      vertLine: {
        style: 3,
        color: "#080F25",
      },
      horzLine: {
        style: 3,
        color: "#080F25",
      },
    },
    layout: {
      panes: {
        separatorColor: "#080F25",
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* 控制面板 */}
      <RealTimeControls />

      {/* 图表容器 */}
      <Card className="w-full">
        <CardContent>
          <div className="relative h-96 w-full">
            <Chart
              options={chartOptions}
              onCrosshairMove={onCrosshairMove}
              onInit={onChartInit}
            >
              <Pane>
                <CandlestickSeries 
                  ref={ref}
                  data={realTimeData} 
                  reactive={false}
                  // options={{
                  //   upColor: '#22c55e',
                  //   downColor: '#ef4444',
                  //   borderUpColor: '#22c55e',
                  //   borderDownColor: '#ef4444',
                  //   wickUpColor: '#22c55e',
                  //   wickDownColor: '#ef4444',
                  // }}
                />
                <KlineLegend klineSeriesData={legendData} />
              </Pane>
              {/* <TimeScale>
                <TimeScaleFitContentTrigger 
                  deps={resizeOnUpdate ? [realTimeData.length] : []} 
                />
              </TimeScale> */}
            </Chart>
            
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BacktestChart;