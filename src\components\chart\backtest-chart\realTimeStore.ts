import { create } from "zustand";
import type { Kline } from "@/types/kline";
import type { SeriesApiRef } from "lightweight-charts-react-components";
import type { CandlestickData, Time } from "lightweight-charts";

interface RealTimeKlineStore {
  reactive: boolean;
  resizeOnUpdate: boolean;
  data: Kline[];
  seriesRef: SeriesApiRef<"Candlestick"> | null;

  setReactive: (reactive: boolean) => void;
  setResizeOnUpdate: (resizeOnUpdate: boolean) => void;
  setData: (data: Kline[]) => void;
  setSeriesRef: (ref: SeriesApiRef<"Candlestick">) => void;
  updateSinglePoint: (point: CandlestickData<Time>) => void;
  resetData: () => void;
}

export const useRealTimeKlineStore = create<RealTimeKlineStore>((set, get) => ({
  reactive: true,
  resizeOnUpdate: false,
  data: [], // 初始化为空数组，等待真实数据
  seriesRef: null,
  
  setReactive: (reactive: boolean) => set({ reactive }),
  setResizeOnUpdate: (resizeOnUpdate: boolean) => set({ resizeOnUpdate }),
  setData: (data: Kline[]) => set(() => ({ data })),
  setSeriesRef: (ref: SeriesApiRef<"Candlestick">) => set({ seriesRef: ref }),

  updateSinglePoint: (point: CandlestickData<Time>) => {
    const state = get();
    console.log("updateSinglePoint被调用");
    console.log("seriesRef:", state.seriesRef);
    
    if (state.seriesRef) {
      const series = state.seriesRef.api();
      console.log("series API:", series);
      
      if (series) {
        // 使用 series.update 方法更新单个数据点
        console.log("更新数据点", point);
        try {
          series.update(point);
          console.log("成功更新数据点");
        } catch (error) {
          console.error("更新数据点时出错:", error);
        }
        
        // updateSinglePoint 只负责更新图表显示，不更新 store 中的原始数据
        // 原始数据的更新由其他方法负责
      } else {
        console.log("series API为null，seriesRef存在但API不可用");
      }
    } else {
      console.log("seriesRef为null，series引用不可用");
    }
  },

  resetData: () => {
    set({ data: [] }); // 重置为空数组，等待重新加载真实数据
  },
}));