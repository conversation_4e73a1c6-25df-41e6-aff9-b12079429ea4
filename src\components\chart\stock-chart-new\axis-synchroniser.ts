import {
	type <PERSON><PERSON><PERSON>2<PERSON>,
	EventHandler,
	type NumberRange,
	type VisibleRangeChangedArgs,
} from "scichart";

export class AxisSynchroniser {
	public visibleRange: NumberRange;
	private axes: AxisBase2D[] = [];
	public visibleRangeChanged: EventHandler<VisibleRangeChangedArgs> =
		new EventHandler<VisibleRangeChangedArgs>();

	public constructor(initialRange: NumberRange, axes?: AxisBase2D[]) {
		this.visibleRange = initialRange;
		this.publishChange = this.publishChange.bind(this);
		if (axes) {
			axes.forEach((a) => this.addAxis(a));
		}
	}

	public publishChange(data: VisibleRangeChangedArgs | undefined) {
		if (!data) return;
		this.visibleRange = data.visibleRange;
		this.axes.forEach((a) => (a.visibleRange = this.visibleRange));
		this.visibleRangeChanged.raiseEvent(data);
	}

	public addAxis(axis: AxisBase2D) {
		if (!this.axes.includes(axis)) {
			this.axes.push(axis);
			axis.visibleRange = this.visibleRange;
			axis.visibleRangeChanged.subscribe((data) => this.publishChange(data));
		}
	}

	public removeAxis(axis: AxisBase2D) {
		const index = this.axes.findIndex((a) => a === axis);
		if (index >= 0) {
			this.axes.splice(index, 1);
			axis.visibleRangeChanged.unsubscribe((data) => this.publishChange(data));
		}
	}
}
