import React, { useCallback, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	PositionOperation,
	type PositionOperationConfig,
} from "@/types/node/position-management-node";
import OperationTypeSelector from "./operation-type-selector";
import SymbolSelector from "./symbol-selector";

interface OperationConfigDialogProps {
	isOpen: boolean;
	isEditing: boolean;
	editingConfig?: PositionOperationConfig;
	onOpenChange: (open: boolean) => void;
	onSave: (config: PositionOperationConfig) => void;
	existingConfigs: PositionOperationConfig[];
	editingIndex: number | null;
}

const OperationConfigDialog: React.FC<OperationConfigDialogProps> = ({
	isOpen,
	isEditing,
	editingConfig,
	onOpenChange,
	onSave,
	existingConfigs,
	editingIndex,
}) => {
	// 表单状态
	const [symbol, setSymbol] = React.useState<string | null>(null);
	const [operationType, setOperationType] = React.useState<PositionOperation>(
		PositionOperation.UPDATE,
	);
	const [operationName, setOperationName] = React.useState<string>("");
	const [isNameAutoGenerated, setIsNameAutoGenerated] =
		React.useState<boolean>(true);

	// 获取操作类型的中文名称
	const getOperationTypeLabel = (type: PositionOperation) => {
		const labels = {
			[PositionOperation.UPDATE]: "更新仓位",
			[PositionOperation.CLOSEALL]: "全部平仓",
		};
		return labels[type] || type;
	};

	// 生成默认操作名称
	const generateOperationName = useCallback(
		(type: PositionOperation) => {
			const typeLabel = getOperationTypeLabel(type);
			const nextIndex = existingConfigs.length + 1;
			return `${typeLabel}${nextIndex}`;
		},
		[existingConfigs.length],
	);

	const resetForm = () => {
		setSymbol(null);
		setOperationType(PositionOperation.UPDATE);
		setOperationName("");
		setIsNameAutoGenerated(true);
	};

	// 当对话框打开时重置或恢复状态
	useEffect(() => {
		if (isOpen) {
			if (isEditing && editingConfig) {
				setSymbol(editingConfig.symbol);
				setOperationType(editingConfig.positionOperation);
				setOperationName(editingConfig.positionOperationName);
				setIsNameAutoGenerated(false); // 编辑时不是自动生成的
			} else {
				resetForm();
				// 新建时生成默认名称
				const defaultName = generateOperationName(PositionOperation.UPDATE);
				setOperationName(defaultName);
				setIsNameAutoGenerated(true);
			}
		}
	}, [
		isOpen,
		isEditing,
		editingConfig,
		existingConfigs.length,
		generateOperationName,
	]);

	// 当操作类型改变时，如果名称是自动生成的，则重新生成
	const handleOperationTypeChange = (newType: PositionOperation) => {
		setOperationType(newType);
		if (isNameAutoGenerated || !operationName.trim()) {
			const newName = generateOperationName(newType);
			setOperationName(newName);
			setIsNameAutoGenerated(true);
		}
	};

	// 当用户手动修改操作名称时，标记为非自动生成
	const handleOperationNameChange = (value: string) => {
		setOperationName(value);
		setIsNameAutoGenerated(false);
	};

	// 检查是否存在重复配置
	const isDuplicate = () => {
		return existingConfigs.some(
			(config, index) =>
				index !== editingIndex &&
				config.symbol === symbol &&
				config.positionOperation === operationType,
		);
	};

	const handleSave = () => {
		// 静默验证，不满足条件直接返回
		if (!operationName.trim()) {
			return;
		}

		if (isDuplicate()) {
			return;
		}

		const operationConfig: PositionOperationConfig = {
			positionOperationId: editingConfig?.positionOperationId || 0, // 编辑时保持原有id，新增时由主组件设置
			inputHandleId: editingConfig?.inputHandleId || "",
			symbol: symbol && symbol.trim() ? symbol.trim().toUpperCase() : null,
			positionOperation: operationType,
			positionOperationName: operationName.trim(),
		};

		onSave(operationConfig);
		onOpenChange(false);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[424px]">
				<DialogHeader>
					<DialogTitle>
						{isEditing ? "编辑仓位操作" : "添加仓位操作"}
					</DialogTitle>
					<DialogDescription>
						配置仓位操作的参数，包括交易对、操作类型和操作名称。
					</DialogDescription>
				</DialogHeader>
				<div className="flex flex-col gap-4 py-4">
					<div className="flex flex-col gap-2">
						<Label htmlFor="symbol" className="text-sm font-medium">
							交易对
						</Label>
						<div className="w-full">
							<SymbolSelector
								value={symbol || ""}
								onChange={(value) => setSymbol(value)}
								allowEmpty={true}
							/>
						</div>
					</div>

					<div className="flex flex-col gap-2">
						<Label htmlFor="operationType" className="text-sm font-medium">
							操作类型
						</Label>
						<div className="w-full">
							<OperationTypeSelector
								value={operationType}
								onChange={handleOperationTypeChange}
							/>
						</div>
					</div>

					<div className="flex flex-col gap-2">
						<Label htmlFor="operationName" className="text-sm font-medium">
							操作名称
						</Label>
						<Input
							id="operationName"
							type="text"
							value={operationName}
							onChange={(e) => handleOperationNameChange(e.target.value)}
							placeholder="输入操作名称"
							className="w-full"
						/>
					</div>
				</div>
				<DialogFooter>
					<Button variant="outline" onClick={() => onOpenChange(false)}>
						取消
					</Button>
					<Button
						onClick={handleSave}
						disabled={!operationName.trim() || isDuplicate()}
					>
						保存
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default OperationConfigDialog;
