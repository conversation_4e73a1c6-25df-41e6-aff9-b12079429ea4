import { <PERSON><PERSON><PERSON>, Bo<PERSON>, Play, Settings2, SquareTerminal } from "lucide-react";

export const sideBarMenu = {
	user: {
		name: "<PERSON><PERSON><PERSON>",
		email: "<EMAIL>",
		avatar: "/avatars/shadcn.jpg",
	},
	navMain: [
		{
			title: "策略列表",
			url: "/strategy-list",
			icon: SquareTerminal,
			isActive: true,
		},
		{
			title: "账户管理",
			url: "/account",
			icon: Bo<PERSON>,
		},
		{
			title: "测试页面",
			url: "/test",
			icon: BookOpen,
		},
		{
			title: "设置",
			url: "/setting",
			icon: Settings2,
		},
		{
			title: "策略流",
			url: "/strategy-flow",
			icon: Play,
		},
	],
};
